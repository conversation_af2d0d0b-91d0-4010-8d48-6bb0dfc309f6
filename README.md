# Windows定时任务查看器

一个基于Python Flask开发的Web应用，用于查看和管理Windows系统中的定时任务。

## 功能特性

- 📋 **任务列表展示** - 以表格形式展示所有定时任务
- 🔍 **智能搜索** - 支持按任务名称、状态、命令等搜索
- 🏷️ **状态过滤** - 按任务状态（就绪、运行中、已禁用等）过滤
- 📁 **文件夹分类** - 按任务文件夹分类查看
- 📊 **统计信息** - 显示任务总数、启用数、运行数等统计
- 📄 **详情查看** - 查看任务的详细配置信息
- 🔄 **实时刷新** - 支持手动刷新任务列表
- 📱 **响应式设计** - 支持桌面和移动设备访问
- ⚡ **缓存机制** - 提高数据加载性能
- 📝 **日志记录** - 完整的操作日志记录

## 系统要求

- Windows 10/11 或 Windows Server 2016+
- Python 3.7+
- 管理员权限（用于访问系统定时任务）

## 安装步骤

### 1. 克隆项目

```bash
git clone <repository-url>
cd WindowsSchelduleView
```

### 2. 创建虚拟环境

```bash
python -m venv venv
venv\Scripts\activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 运行应用

```bash
python run.py
```

或者直接运行：

```bash
python app.py
```

### 5. 访问应用

打开浏览器访问：http://localhost:5000

## 配置说明

### 环境变量

- `FLASK_CONFIG` - 配置环境（development/production，默认：development）
- `HOST` - 服务监听地址（默认：0.0.0.0）
- `PORT` - 服务端口（默认：5000）
- `LOG_LEVEL` - 日志级别（DEBUG/INFO/WARNING/ERROR，默认：INFO）
- `TASK_CACHE_TIMEOUT` - 任务缓存超时时间（秒，默认：300）

### 配置文件

主要配置在 `config.py` 文件中：

```python
# 开发环境
export FLASK_CONFIG=development

# 生产环境
export FLASK_CONFIG=production
```

## 项目结构

```
WindowsSchelduleView/
├── app.py                 # 主应用文件
├── run.py                 # 启动脚本
├── config.py              # 配置文件
├── task_manager.py        # 任务管理器
├── data_processor.py      # 数据处理器
├── requirements.txt       # 依赖包列表
├── templates/             # HTML模板
│   └── index.html
├── static/                # 静态资源
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── app.js
└── logs/                  # 日志文件目录
```

## API接口

### 获取任务列表

```
GET /api/tasks
```

参数：
- `refresh` - 是否强制刷新缓存（true/false）

### 获取任务详情

```
GET /api/task/<task_name>
```

### 健康检查

```
GET /api/health
```

## 使用说明

### 1. 查看任务列表

- 启动应用后，主页面会自动加载所有定时任务
- 任务按名称排序显示，支持点击表头排序
- 显示任务状态、下次运行时间、上次运行结果等信息

### 2. 搜索和过滤

- **搜索框**：输入关键词搜索任务名称、状态或命令
- **状态过滤**：选择特定状态查看对应任务
- **文件夹过滤**：按任务所在文件夹分类查看

### 3. 查看任务详情

- 点击任务行的"详情"按钮查看完整配置
- 包括基本信息、触发器、操作、设置等详细信息

### 4. 刷新数据

- 点击"刷新"按钮获取最新任务状态
- 系统会自动缓存数据，提高加载速度

## 权限要求

本应用需要管理员权限才能正常访问Windows定时任务信息：

1. 右键点击命令提示符或PowerShell
2. 选择"以管理员身份运行"
3. 在管理员命令行中启动应用

## 故障排除

### 1. 权限不足

**问题**：无法获取任务列表或显示"拒绝访问"错误

**解决**：确保以管理员权限运行应用

### 2. 编码问题

**问题**：任务名称显示乱码

**解决**：检查系统编码设置，确保支持中文显示

### 3. 端口占用

**问题**：启动时提示端口被占用

**解决**：修改端口号或停止占用端口的程序

```bash
# 修改端口
set PORT=8080
python run.py
```

### 4. 依赖包问题

**问题**：导入模块失败

**解决**：重新安装依赖包

```bash
pip install -r requirements.txt --force-reinstall
```

## 开发说明

### 添加新功能

1. 在相应模块中添加功能代码
2. 更新API接口（如需要）
3. 修改前端页面（如需要）
4. 添加相应的错误处理和日志记录

### 代码结构

- `task_manager.py` - 负责与Windows系统交互，获取任务数据
- `data_processor.py` - 负责数据格式化和处理
- `app.py` - Flask应用主文件，定义路由和API
- `config.py` - 应用配置管理

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 基本的任务查看功能
- 搜索和过滤功能
- 响应式Web界面
