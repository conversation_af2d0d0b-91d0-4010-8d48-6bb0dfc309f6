"""
Windows定时任务查看器 - 主应用文件
"""
from flask import Flask, render_template, jsonify, request
from task_manager import WindowsTaskManager
from data_processor import TaskDataProcessor
from config import config
import logging
import os
import traceback
from datetime import datetime, timed<PERSON><PERSON>

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 初始化配置
    config[config_name].init_app(app)

    return app

app = create_app()
logger = logging.getLogger(__name__)

# 初始化任务管理器和数据处理器
task_manager = WindowsTaskManager()
data_processor = TaskDataProcessor()

# 任务缓存
task_cache = {
    'data': None,
    'timestamp': None,
    'timeout': app.config.get('TASK_CACHE_TIMEOUT', 300)
}

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

def is_cache_valid():
    """检查缓存是否有效"""
    if task_cache['data'] is None or task_cache['timestamp'] is None:
        return False

    cache_age = (datetime.now() - task_cache['timestamp']).total_seconds()
    return cache_age < task_cache['timeout']

def update_task_cache():
    """更新任务缓存"""
    try:
        logger.info("开始更新任务缓存")
        raw_tasks = task_manager.get_all_tasks()
        formatted_tasks = data_processor.format_tasks(raw_tasks)

        task_cache['data'] = formatted_tasks
        task_cache['timestamp'] = datetime.now()

        logger.info(f"任务缓存更新完成，共 {len(formatted_tasks)} 个任务")
        return formatted_tasks
    except Exception as e:
        logger.error(f"更新任务缓存失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise

@app.route('/api/tasks')
def get_tasks():
    """获取所有定时任务的API接口"""
    try:
        # 检查是否需要刷新缓存
        force_refresh = request.args.get('refresh', 'false').lower() == 'true'

        if force_refresh or not is_cache_valid():
            formatted_tasks = update_task_cache()
        else:
            formatted_tasks = task_cache['data']
            logger.debug("使用缓存的任务数据")

        return jsonify({
            'success': True,
            'data': formatted_tasks,
            'count': len(formatted_tasks),
            'cached': not force_refresh and task_cache['timestamp'] is not None,
            'cache_time': task_cache['timestamp'].isoformat() if task_cache['timestamp'] else None
        })
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': '获取任务列表失败，请检查系统权限或稍后重试',
            'details': str(e) if app.debug else None
        }), 500

@app.route('/api/task/<path:task_name>')
def get_task_detail(task_name):
    """获取特定任务详情的API接口"""
    try:
        logger.info(f"获取任务详情: {task_name}")

        # 获取任务详细信息
        task_detail = task_manager.get_task_detail(task_name)

        if task_detail:
            # 格式化任务详情
            formatted_detail = data_processor.format_task_detail(task_detail)
            return jsonify({
                'success': True,
                'data': formatted_detail
            })
        else:
            logger.warning(f"任务未找到: {task_name}")
            return jsonify({
                'success': False,
                'error': f'任务 "{task_name}" 未找到'
            }), 404

    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': '获取任务详情失败，请稍后重试',
            'details': str(e) if app.debug else None
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': '请求的资源未找到'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"内部服务器错误: {str(error)}")
    return jsonify({
        'success': False,
        'error': '内部服务器错误，请稍后重试'
    }), 500

@app.route('/api/health')
def health_check():
    """健康检查接口"""
    try:
        # 简单的健康检查
        return jsonify({
            'success': True,
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'cache_status': 'valid' if is_cache_valid() else 'expired'
        })
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("启动Windows定时任务查看器")
    app.run(debug=app.config['DEBUG'], host='0.0.0.0', port=5000)
