"""
应用配置文件
"""
import os
import logging
from datetime import datetime

class Config:
    """应用配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'windows-task-viewer-secret-key'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO').upper()
    LOG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    LOG_FILE = os.path.join(LOG_DIR, f'app_{datetime.now().strftime("%Y%m%d")}.log')
    
    # 任务配置
    TASK_CACHE_TIMEOUT = int(os.environ.get('TASK_CACHE_TIMEOUT', '300'))  # 5分钟缓存
    MAX_TASKS_PER_REQUEST = int(os.environ.get('MAX_TASKS_PER_REQUEST', '1000'))
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建日志目录
        if not os.path.exists(Config.LOG_DIR):
            os.makedirs(Config.LOG_DIR)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, Config.LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        # 设置第三方库日志级别
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        
        app.logger.info('应用配置初始化完成')

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'INFO'

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
