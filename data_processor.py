"""
数据处理和格式化模块
将原始任务数据转换为前端友好的格式
"""
import re
from datetime import datetime
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class TaskDataProcessor:
    """任务数据处理器类"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.status_mapping = {
            'Ready': '就绪',
            'Running': '运行中',
            'Disabled': '已禁用',
            'Queued': '排队中',
            'Unknown': '未知'
        }
        
        self.trigger_type_mapping = {
            'TimeTrigger': '时间触发',
            'DailyTrigger': '每日触发',
            'WeeklyTrigger': '每周触发',
            'MonthlyTrigger': '每月触发',
            'BootTrigger': '启动时触发',
            'LogonTrigger': '登录时触发',
            'EventTrigger': '事件触发',
            'IdleTrigger': '空闲时触发'
        }
    
    def format_tasks(self, raw_tasks: List[Dict]) -> List[Dict]:
        """
        格式化任务列表数据
        
        Args:
            raw_tasks (List[Dict]): 原始任务数据列表
            
        Returns:
            List[Dict]: 格式化后的任务列表
        """
        formatted_tasks = []
        
        for task in raw_tasks:
            try:
                formatted_task = self._format_single_task(task)
                if formatted_task:
                    formatted_tasks.append(formatted_task)
            except Exception as e:
                logger.warning(f"格式化任务时出错: {str(e)}")
                continue
        
        # 按任务名称排序
        formatted_tasks.sort(key=lambda x: x.get('name', '').lower())
        
        return formatted_tasks
    
    def _format_single_task(self, task: Dict) -> Dict:
        """
        格式化单个任务数据
        
        Args:
            task (Dict): 原始任务数据
            
        Returns:
            Dict: 格式化后的任务数据
        """
        # 获取任务名称（去除路径前缀）
        task_name = task.get('TaskName', '').strip()
        if task_name.startswith('\\'):
            task_name = task_name[1:]
        
        # 格式化状态
        status = task.get('Status', 'Unknown')
        status_cn = self.status_mapping.get(status, status)
        
        # 格式化下次运行时间
        next_run_time = self._format_datetime(task.get('Next Run Time', ''))
        
        # 格式化上次运行时间
        last_run_time = self._format_datetime(task.get('Last Run Time', ''))
        
        # 格式化上次运行结果
        last_result = task.get('Last Result', '')
        last_result_text = self._format_result_code(last_result)
        
        # 获取任务路径和文件夹
        task_path = task.get('TaskName', '')
        folder = self._extract_folder_from_path(task_path)
        
        return {
            'name': task_name,
            'full_path': task_path,
            'folder': folder,
            'status': status,
            'status_cn': status_cn,
            'next_run_time': next_run_time,
            'last_run_time': last_run_time,
            'last_result': last_result,
            'last_result_text': last_result_text,
            'author': task.get('Author', ''),
            'task_to_run': task.get('Task To Run', ''),
            'start_in': task.get('Start In', ''),
            'comment': task.get('Comment', ''),
            'scheduled_task_state': task.get('Scheduled Task State', ''),
            'idle_time': task.get('Idle Time', ''),
            'power_management': task.get('Power Management', ''),
            'run_as_user': task.get('Run As User', ''),
            'delete_task_if_not_rescheduled': task.get('Delete Task If Not Rescheduled', ''),
            'stop_task_if_runs_x_hours_and_x_mins': task.get('Stop Task If Runs X Hours And X Mins', ''),
            'schedule': task.get('Schedule', ''),
            'schedule_type': task.get('Schedule Type', ''),
            'start_time': task.get('Start Time', ''),
            'start_date': task.get('Start Date', ''),
            'end_date': task.get('End Date', ''),
            'days': task.get('Days', ''),
            'months': task.get('Months', ''),
            'repeat_every': task.get('Repeat: Every', ''),
            'repeat_until_time': task.get('Repeat: Until: Time', ''),
            'repeat_until_duration': task.get('Repeat: Until: Duration', ''),
            'repeat_stop_if_still_running': task.get('Repeat: Stop If Still Running', '')
        }
    
    def format_task_detail(self, task_detail: Dict) -> Dict:
        """
        格式化任务详细信息
        
        Args:
            task_detail (Dict): 原始任务详细信息
            
        Returns:
            Dict: 格式化后的任务详细信息
        """
        formatted_detail = {
            'basic_info': {
                'name': task_detail.get('name', ''),
                'description': task_detail.get('description', ''),
                'author': task_detail.get('author', ''),
                'version': task_detail.get('version', ''),
                'date': self._format_datetime(task_detail.get('date', ''))
            },
            'triggers': [],
            'actions': [],
            'settings': self._format_settings(task_detail.get('settings', {})),
            'security': self._format_principals(task_detail.get('principals', {}))
        }
        
        # 格式化触发器
        for trigger in task_detail.get('triggers', []):
            formatted_trigger = self._format_trigger(trigger)
            formatted_detail['triggers'].append(formatted_trigger)
        
        # 格式化操作
        for action in task_detail.get('actions', []):
            formatted_action = self._format_action(action)
            formatted_detail['actions'].append(formatted_action)
        
        return formatted_detail
    
    def _format_trigger(self, trigger: Dict) -> Dict:
        """格式化触发器信息"""
        trigger_type = trigger.get('type', '')
        trigger_type_cn = self.trigger_type_mapping.get(trigger_type, trigger_type)
        
        return {
            'type': trigger_type,
            'type_cn': trigger_type_cn,
            'enabled': trigger.get('enabled', 'true') == 'true',
            'start_time': self._format_datetime(trigger.get('start_boundary', '')),
            'end_time': self._format_datetime(trigger.get('end_boundary', '')),
            'repetition': self._format_repetition(trigger.get('repetition', {}))
        }
    
    def _format_action(self, action: Dict) -> Dict:
        """格式化操作信息"""
        return {
            'type': action.get('type', ''),
            'command': action.get('command', ''),
            'arguments': action.get('arguments', ''),
            'working_directory': action.get('working_directory', ''),
            'display_command': self._format_command_display(
                action.get('command', ''), 
                action.get('arguments', '')
            )
        }
    
    def _format_settings(self, settings: Dict) -> Dict:
        """格式化设置信息"""
        return {
            'enabled': settings.get('Enabled', 'true') == 'true',
            'allow_demand_start': settings.get('AllowDemandStart', 'true') == 'true',
            'allow_hard_terminate': settings.get('AllowHardTerminate', 'true') == 'true',
            'start_when_available': settings.get('StartWhenAvailable', 'false') == 'true',
            'run_only_if_network_available': settings.get('RunOnlyIfNetworkAvailable', 'false') == 'true',
            'hidden': settings.get('Hidden', 'false') == 'true',
            'run_only_if_idle': settings.get('RunOnlyIfIdle', 'false') == 'true',
            'wake_to_run': settings.get('WakeToRun', 'false') == 'true',
            'execution_time_limit': settings.get('ExecutionTimeLimit', ''),
            'priority': settings.get('Priority', ''),
            'delete_expired_task_after': settings.get('DeleteExpiredTaskAfter', ''),
            'multiple_instances_policy': settings.get('MultipleInstancesPolicy', '')
        }
    
    def _format_principals(self, principals: Dict) -> Dict:
        """格式化主体信息"""
        return {
            'user_id': principals.get('user_id', ''),
            'logon_type': principals.get('logon_type', ''),
            'run_level': principals.get('run_level', '')
        }
    
    def _format_repetition(self, repetition: Dict) -> Dict:
        """格式化重复设置"""
        return {
            'interval': self._format_duration(repetition.get('interval', '')),
            'duration': self._format_duration(repetition.get('duration', ''))
        }
    
    def _format_datetime(self, datetime_str: str) -> str:
        """格式化日期时间字符串"""
        if not datetime_str or datetime_str.lower() in ['n/a', 'never', 'disabled']:
            return datetime_str
        
        try:
            # 尝试解析ISO格式的日期时间
            if 'T' in datetime_str:
                dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # 尝试解析其他常见格式
            for fmt in ['%m/%d/%Y %I:%M:%S %p', '%Y-%m-%d %H:%M:%S', '%m/%d/%Y %H:%M:%S']:
                try:
                    dt = datetime.strptime(datetime_str, fmt)
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue
            
            return datetime_str
        except Exception:
            return datetime_str
    
    def _format_duration(self, duration_str: str) -> str:
        """格式化持续时间字符串"""
        if not duration_str:
            return ''
        
        # 解析ISO 8601持续时间格式 (PT1H30M)
        if duration_str.startswith('PT'):
            duration_str = duration_str[2:]  # 移除PT前缀
            
            hours = 0
            minutes = 0
            seconds = 0
            
            # 提取小时
            hour_match = re.search(r'(\d+)H', duration_str)
            if hour_match:
                hours = int(hour_match.group(1))
            
            # 提取分钟
            minute_match = re.search(r'(\d+)M', duration_str)
            if minute_match:
                minutes = int(minute_match.group(1))
            
            # 提取秒
            second_match = re.search(r'(\d+)S', duration_str)
            if second_match:
                seconds = int(second_match.group(1))
            
            # 格式化输出
            parts = []
            if hours > 0:
                parts.append(f'{hours}小时')
            if minutes > 0:
                parts.append(f'{minutes}分钟')
            if seconds > 0:
                parts.append(f'{seconds}秒')
            
            return ''.join(parts) if parts else duration_str
        
        return duration_str
    
    def _format_result_code(self, result_code: str) -> str:
        """格式化结果代码"""
        if not result_code:
            return ''
        
        # 常见的结果代码映射
        result_mapping = {
            '0': '成功',
            '0x0': '成功',
            '0x1': '错误的函数',
            '0x2': '系统找不到指定的文件',
            '0x3': '系统找不到指定的路径',
            '0x5': '拒绝访问',
            '0x20': '进程无法访问文件，因为另一个程序正在使用此文件',
            '0x41301': '任务当前正在运行',
            '0x41302': '任务已禁用',
            '0x41303': '任务尚未运行',
            '0x41304': '没有更多的运行',
            '0x41306': '上次运行被用户终止',
            '0x8004130F': '凭据变为无效',
            '0x80041310': '任务不存在',
            '0x80041315': '任务XML包含意外的节点'
        }
        
        return result_mapping.get(result_code, f'错误代码: {result_code}')
    
    def _extract_folder_from_path(self, task_path: str) -> str:
        """从任务路径中提取文件夹名称"""
        if not task_path or not task_path.startswith('\\'):
            return '根目录'
        
        path_parts = task_path.strip('\\').split('\\')
        if len(path_parts) > 1:
            return '\\'.join(path_parts[:-1])
        
        return '根目录'
    
    def _format_command_display(self, command: str, arguments: str) -> str:
        """格式化命令显示"""
        if not command:
            return ''
        
        display_command = command
        if arguments:
            display_command += f' {arguments}'
        
        # 如果命令太长，截断显示
        if len(display_command) > 100:
            display_command = display_command[:97] + '...'
        
        return display_command
