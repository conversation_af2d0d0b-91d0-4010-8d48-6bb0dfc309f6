#!/usr/bin/env python3
"""
调试CSV输出格式
"""
import subprocess
import os

def test_schtasks_output():
    """测试schtasks命令输出"""
    try:
        # 使用schtasks命令获取任务列表
        cmd = ['schtasks', '/query', '/fo', 'csv', '/v']
        
        # 设置英文环境变量
        env = os.environ.copy()
        env['LANG'] = 'en_US.UTF-8'
        env['LC_ALL'] = 'en_US.UTF-8'
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='gbk', env=env)
        
        if result.returncode != 0:
            print(f"命令执行失败: {result.stderr}")
            return
        
        lines = result.stdout.strip().split('\n')
        print(f"总行数: {len(lines)}")
        
        if lines:
            print(f"\n第一行（表头）:")
            print(f"长度: {len(lines[0])}")
            print(f"内容: {repr(lines[0])}")
            
            # 解析表头
            headers = [h.strip('"') for h in lines[0].split('","')]
            print(f"\n解析后的表头 ({len(headers)} 个字段):")
            for i, header in enumerate(headers):
                print(f"  {i}: {repr(header)}")
        
        if len(lines) > 1:
            print(f"\n第二行（第一个任务）:")
            print(f"长度: {len(lines[1])}")
            print(f"内容: {repr(lines[1])}")
            
            # 解析第一个任务
            fields = []
            current_field = ""
            in_quotes = False
            
            for char in lines[1]:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    fields.append(current_field.strip('"'))
                    current_field = ""
                else:
                    current_field += char
            
            # 添加最后一个字段
            fields.append(current_field.strip('"'))
            
            print(f"\n解析后的字段 ({len(fields)} 个):")
            for i, field in enumerate(fields):
                print(f"  {i}: {repr(field)}")
            
            # 匹配表头和字段
            if len(headers) == len(fields):
                print(f"\n字段匹配:")
                for i, (header, field) in enumerate(zip(headers, fields)):
                    print(f"  {header}: {field}")
            else:
                print(f"\n字段数量不匹配: 表头{len(headers)}个，字段{len(fields)}个")
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == '__main__':
    test_schtasks_output()
