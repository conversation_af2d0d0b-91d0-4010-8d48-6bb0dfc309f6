#!/usr/bin/env python3
"""
Windows定时任务查看器启动脚本
"""
import os
import sys
import logging
from app import create_app

def main():
    """主函数"""
    # 设置环境变量
    os.environ.setdefault('FLASK_CONFIG', 'development')
    
    # 创建应用
    app = create_app()
    
    # 获取配置
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = app.config.get('DEBUG', False)
    
    print(f"""
╔══════════════════════════════════════════════════════════════╗
║                Windows定时任务查看器                          ║
║                                                              ║
║  服务地址: http://{host}:{port}                              ║
║  调试模式: {'开启' if debug else '关闭'}                                    ║
║  配置环境: {os.environ.get('FLASK_CONFIG', 'development')}                                ║
║                                                              ║
║  按 Ctrl+C 停止服务                                          ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
