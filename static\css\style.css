/* Windows定时任务查看器样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header .subtitle {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 控制面板样式 */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 20px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-controls select {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    background: white;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-info {
    background: #17a2b8;
    color: white;
    padding: 5px 10px;
    font-size: 12px;
}

.btn-info:hover {
    background: #138496;
}

/* 统计卡片样式 */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
}

/* 加载和错误消息样式 */
.loading, .error-message {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading i {
    font-size: 2em;
    color: #667eea;
    margin-bottom: 10px;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.error-message i {
    color: #721c24;
    margin-right: 10px;
}

/* 表格样式 */
.task-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.task-table {
    width: 100%;
    border-collapse: collapse;
}

.task-table th,
.task-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.task-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.task-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;
}

.task-table th.sortable:hover {
    background: #e9ecef;
}

.task-table th.sortable i {
    margin-left: 5px;
    opacity: 0.5;
}

.task-table th.sortable.asc i:before {
    content: "\f0de";
    opacity: 1;
}

.task-table th.sortable.desc i:before {
    content: "\f0dd";
    opacity: 1;
}

.task-table tbody tr:hover {
    background: #f8f9fa;
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 60px;
}

.status-ready {
    background: #d4edda;
    color: #155724;
}

.status-running {
    background: #d1ecf1;
    color: #0c5460;
}

.status-disabled {
    background: #f8d7da;
    color: #721c24;
}

.status-queued {
    background: #fff3cd;
    color: #856404;
}

.status-unknown {
    background: #e2e3e5;
    color: #383d41;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
}

#pageInfo {
    font-weight: 500;
    color: #666;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    animation: slideIn 0.3s;
}

.modal-header {
    background: #667eea;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* 任务详情样式 */
.detail-section {
    margin-bottom: 25px;
}

.detail-section h3 {
    color: #667eea;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 2px solid #eee;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.detail-item {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.detail-value {
    color: #666;
    word-break: break-all;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .task-table-container {
        overflow-x: auto;
    }
    
    .task-table {
        min-width: 800px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
