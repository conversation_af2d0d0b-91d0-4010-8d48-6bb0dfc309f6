// Windows定时任务查看器 JavaScript

class TaskViewer {
    constructor() {
        this.tasks = [];
        this.filteredTasks = [];
        this.currentPage = 1;
        this.tasksPerPage = 20;
        this.sortColumn = 'name';
        this.sortDirection = 'asc';
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadTasks();
    }
    
    bindEvents() {
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.filterTasks();
        });
        
        // 状态过滤
        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filterTasks();
        });
        
        // 文件夹过滤
        document.getElementById('folderFilter').addEventListener('change', (e) => {
            this.filterTasks();
        });
        
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadTasks();
        });
        
        // 分页按钮
        document.getElementById('prevPage').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.renderTable();
            }
        });
        
        document.getElementById('nextPage').addEventListener('click', () => {
            const totalPages = Math.ceil(this.filteredTasks.length / this.tasksPerPage);
            if (this.currentPage < totalPages) {
                this.currentPage++;
                this.renderTable();
            }
        });
        
        // 表格排序
        document.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', (e) => {
                const column = e.currentTarget.dataset.sort;
                this.sortTasks(column);
            });
        });
        
        // 模态框关闭
        document.getElementById('modalClose').addEventListener('click', () => {
            this.closeModal();
        });
        
        // 点击模态框外部关闭
        document.getElementById('taskModal').addEventListener('click', (e) => {
            if (e.target.id === 'taskModal') {
                this.closeModal();
            }
        });
    }
    
    async loadTasks() {
        this.showLoading(true);
        this.hideError();
        
        try {
            const response = await fetch('/api/tasks');
            const data = await response.json();
            
            if (data.success) {
                this.tasks = data.data;
                this.updateFolderFilter();
                this.filterTasks();
                this.updateStats();
            } else {
                this.showError(data.error || '加载任务失败');
            }
        } catch (error) {
            this.showError('网络错误：' + error.message);
        } finally {
            this.showLoading(false);
        }
    }
    
    filterTasks() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const folderFilter = document.getElementById('folderFilter').value;
        
        this.filteredTasks = this.tasks.filter(task => {
            const matchesSearch = !searchTerm || 
                task.name.toLowerCase().includes(searchTerm) ||
                task.status_cn.toLowerCase().includes(searchTerm) ||
                task.task_to_run.toLowerCase().includes(searchTerm) ||
                task.comment.toLowerCase().includes(searchTerm);
            
            const matchesStatus = !statusFilter || task.status_cn === statusFilter;
            const matchesFolder = !folderFilter || task.folder === folderFilter;
            
            return matchesSearch && matchesStatus && matchesFolder;
        });
        
        this.currentPage = 1;
        this.renderTable();
    }
    
    sortTasks(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        
        this.filteredTasks.sort((a, b) => {
            let aVal = a[column] || '';
            let bVal = b[column] || '';
            
            // 处理日期时间排序
            if (column.includes('time')) {
                aVal = new Date(aVal || '1900-01-01');
                bVal = new Date(bVal || '1900-01-01');
            }
            
            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });
        
        this.updateSortIndicators();
        this.renderTable();
    }
    
    updateSortIndicators() {
        document.querySelectorAll('.sortable').forEach(th => {
            th.classList.remove('asc', 'desc');
        });
        
        const currentTh = document.querySelector(`[data-sort="${this.sortColumn}"]`);
        if (currentTh) {
            currentTh.classList.add(this.sortDirection);
        }
    }
    
    renderTable() {
        const tbody = document.getElementById('taskTableBody');
        const startIndex = (this.currentPage - 1) * this.tasksPerPage;
        const endIndex = startIndex + this.tasksPerPage;
        const pageData = this.filteredTasks.slice(startIndex, endIndex);
        
        tbody.innerHTML = '';
        
        pageData.forEach(task => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div style="font-weight: 500;">${this.escapeHtml(task.name)}</div>
                    <div style="font-size: 0.8em; color: #666;">${this.escapeHtml(task.full_path)}</div>
                </td>
                <td>
                    <span class="status-badge ${this.getStatusClass(task.status_cn)}">
                        ${task.status_cn}
                    </span>
                </td>
                <td>${this.escapeHtml(task.folder)}</td>
                <td>${this.formatDateTime(task.next_run_time)}</td>
                <td>${this.formatDateTime(task.last_run_time)}</td>
                <td>
                    <span title="${this.escapeHtml(task.last_result)}">
                        ${this.escapeHtml(task.last_result_text)}
                    </span>
                </td>
                <td>
                    <button class="btn btn-info" onclick="taskViewer.showTaskDetail('${this.escapeHtml(task.name)}')">
                        <i class="fas fa-info-circle"></i> 详情
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
        
        this.updatePagination();
    }
    
    updatePagination() {
        const totalPages = Math.ceil(this.filteredTasks.length / this.tasksPerPage);
        const pageInfo = document.getElementById('pageInfo');
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');
        
        pageInfo.textContent = `第 ${this.currentPage} 页，共 ${totalPages} 页 (${this.filteredTasks.length} 个任务)`;
        
        prevBtn.disabled = this.currentPage <= 1;
        nextBtn.disabled = this.currentPage >= totalPages;
    }
    
    updateStats() {
        const total = this.tasks.length;
        const enabled = this.tasks.filter(t => t.status_cn === '就绪').length;
        const running = this.tasks.filter(t => t.status_cn === '运行中').length;
        const disabled = this.tasks.filter(t => t.status_cn === '已禁用').length;
        
        document.getElementById('totalTasks').textContent = total;
        document.getElementById('enabledTasks').textContent = enabled;
        document.getElementById('runningTasks').textContent = running;
        document.getElementById('disabledTasks').textContent = disabled;
    }
    
    updateFolderFilter() {
        const folders = [...new Set(this.tasks.map(task => task.folder))].sort();
        const folderFilter = document.getElementById('folderFilter');
        
        // 清除现有选项（保留"所有文件夹"）
        while (folderFilter.children.length > 1) {
            folderFilter.removeChild(folderFilter.lastChild);
        }
        
        folders.forEach(folder => {
            const option = document.createElement('option');
            option.value = folder;
            option.textContent = folder;
            folderFilter.appendChild(option);
        });
    }
    
    async showTaskDetail(taskName) {
        try {
            const response = await fetch(`/api/task/${encodeURIComponent(taskName)}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderTaskDetail(data.data);
                this.showModal();
            } else {
                this.showError(data.error || '获取任务详情失败');
            }
        } catch (error) {
            this.showError('网络错误：' + error.message);
        }
    }
    
    renderTaskDetail(taskDetail) {
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');
        
        modalTitle.textContent = `任务详情 - ${taskDetail.basic_info.name}`;
        
        let html = `
            <div class="detail-section">
                <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">任务名称</div>
                        <div class="detail-value">${this.escapeHtml(taskDetail.basic_info.name)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">描述</div>
                        <div class="detail-value">${this.escapeHtml(taskDetail.basic_info.description) || '无'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">作者</div>
                        <div class="detail-value">${this.escapeHtml(taskDetail.basic_info.author) || '无'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">版本</div>
                        <div class="detail-value">${this.escapeHtml(taskDetail.basic_info.version) || '无'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">创建日期</div>
                        <div class="detail-value">${this.formatDateTime(taskDetail.basic_info.date)}</div>
                    </div>
                </div>
            </div>
        `;
        
        // 触发器信息
        if (taskDetail.triggers && taskDetail.triggers.length > 0) {
            html += `
                <div class="detail-section">
                    <h3><i class="fas fa-clock"></i> 触发器</h3>
            `;
            
            taskDetail.triggers.forEach((trigger, index) => {
                html += `
                    <div class="detail-grid" style="margin-bottom: 15px; padding: 10px; border: 1px solid #eee; border-radius: 5px;">
                        <div class="detail-item">
                            <div class="detail-label">类型</div>
                            <div class="detail-value">${this.escapeHtml(trigger.type_cn)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">状态</div>
                            <div class="detail-value">${trigger.enabled ? '启用' : '禁用'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">开始时间</div>
                            <div class="detail-value">${this.formatDateTime(trigger.start_time)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">结束时间</div>
                            <div class="detail-value">${this.formatDateTime(trigger.end_time) || '无限制'}</div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
        }
        
        // 操作信息
        if (taskDetail.actions && taskDetail.actions.length > 0) {
            html += `
                <div class="detail-section">
                    <h3><i class="fas fa-play"></i> 操作</h3>
            `;
            
            taskDetail.actions.forEach((action, index) => {
                html += `
                    <div class="detail-grid" style="margin-bottom: 15px; padding: 10px; border: 1px solid #eee; border-radius: 5px;">
                        <div class="detail-item">
                            <div class="detail-label">类型</div>
                            <div class="detail-value">${this.escapeHtml(action.type)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">命令</div>
                            <div class="detail-value">${this.escapeHtml(action.command)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">参数</div>
                            <div class="detail-value">${this.escapeHtml(action.arguments) || '无'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">工作目录</div>
                            <div class="detail-value">${this.escapeHtml(action.working_directory) || '无'}</div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
        }
        
        modalBody.innerHTML = html;
    }
    
    showModal() {
        document.getElementById('taskModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
        document.getElementById('taskModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    
    showLoading(show) {
        document.getElementById('loading').style.display = show ? 'block' : 'none';
    }
    
    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        errorText.textContent = message;
        errorDiv.style.display = 'block';
    }
    
    hideError() {
        document.getElementById('errorMessage').style.display = 'none';
    }
    
    getStatusClass(status) {
        const statusMap = {
            '就绪': 'status-ready',
            '运行中': 'status-running',
            '已禁用': 'status-disabled',
            '排队中': 'status-queued'
        };
        return statusMap[status] || 'status-unknown';
    }
    
    formatDateTime(dateStr) {
        if (!dateStr || dateStr.toLowerCase() === 'n/a' || dateStr.toLowerCase() === 'never') {
            return '无';
        }
        return dateStr;
    }
    
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
const taskViewer = new TaskViewer();
