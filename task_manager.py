"""
Windows定时任务管理器
用于获取和解析Windows系统中的定时任务信息
"""
import subprocess
import xml.etree.ElementTree as ET
import logging
import os
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class WindowsTaskManager:
    """Windows定时任务管理器类"""
    
    def __init__(self):
        """初始化任务管理器"""
        self.encoding = 'utf-8'
    
    def get_all_tasks(self) -> List[Dict]:
        """
        获取所有定时任务的基本信息

        Returns:
            List[Dict]: 任务列表，每个任务包含基本信息
        """
        try:
            # 使用schtasks命令获取所有任务的列表，强制使用英文输出
            cmd = ['schtasks', '/query', '/fo', 'csv', '/v', '/nh']

            # 设置英文环境变量
            env = os.environ.copy()
            env['LANG'] = 'en_US.UTF-8'
            env['LC_ALL'] = 'en_US.UTF-8'

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='gbk', env=env)

            if result.returncode != 0:
                logger.error(f"获取任务列表失败: {result.stderr}")
                return []

            # 解析CSV输出
            tasks = self._parse_csv_output_chinese(result.stdout)
            logger.info(f"成功获取 {len(tasks)} 个定时任务")
            return tasks

        except Exception as e:
            logger.error(f"获取任务列表时发生异常: {str(e)}")
            return []
    
    def get_task_detail(self, task_name: str) -> Optional[Dict]:
        """
        获取特定任务的详细信息
        
        Args:
            task_name (str): 任务名称
            
        Returns:
            Optional[Dict]: 任务详细信息，如果任务不存在则返回None
        """
        try:
            # 使用schtasks命令获取任务的XML格式详细信息
            cmd = ['schtasks', '/query', '/tn', task_name, '/xml']
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode != 0:
                logger.warning(f"获取任务 '{task_name}' 详情失败: {result.stderr}")
                return None
            
            # 解析XML输出
            task_detail = self._parse_xml_output(result.stdout)
            return task_detail
            
        except Exception as e:
            logger.error(f"获取任务 '{task_name}' 详情时发生异常: {str(e)}")
            return None
    
    def _parse_csv_output(self, csv_output: str) -> List[Dict]:
        """
        解析schtasks CSV输出
        
        Args:
            csv_output (str): CSV格式的输出
            
        Returns:
            List[Dict]: 解析后的任务列表
        """
        tasks = []
        lines = csv_output.strip().split('\n')
        
        if len(lines) < 2:
            return tasks
        
        # 获取表头
        headers = [h.strip('"') for h in lines[0].split('","')]
        
        # 解析每一行数据
        for line in lines[1:]:
            if not line.strip():
                continue
                
            try:
                # 处理CSV行，考虑到可能包含逗号的字段
                fields = []
                current_field = ""
                in_quotes = False
                
                for char in line:
                    if char == '"':
                        in_quotes = not in_quotes
                    elif char == ',' and not in_quotes:
                        fields.append(current_field.strip('"'))
                        current_field = ""
                    else:
                        current_field += char
                
                # 添加最后一个字段
                fields.append(current_field.strip('"'))
                
                # 创建任务字典
                if len(fields) >= len(headers):
                    task = {}
                    for i, header in enumerate(headers):
                        task[header] = fields[i] if i < len(fields) else ""
                    tasks.append(task)
                    
            except Exception as e:
                logger.warning(f"解析CSV行时出错: {str(e)}")
                continue
        
        return tasks

    def _parse_csv_output_chinese(self, csv_output: str) -> List[Dict]:
        """
        解析中文schtasks CSV输出

        Args:
            csv_output (str): CSV格式的输出

        Returns:
            List[Dict]: 解析后的任务列表
        """
        tasks = []
        lines = csv_output.strip().split('\n')

        if len(lines) < 2:
            return tasks

        # 中文到英文字段映射
        field_mapping = {
            '主机名': 'HostName',
            '任务名': 'TaskName',
            '下次运行时间': 'Next Run Time',
            '模式': 'Status',
            '登录状态': 'Logon Mode',
            '上次运行时间': 'Last Run Time',
            '上次结果': 'Last Result',
            '创建者': 'Author',
            '要运行的任务': 'Task To Run',
            '起始于': 'Start In',
            '注释': 'Comment',
            '计划任务状态': 'Scheduled Task State',
            '空闲时间': 'Idle Time',
            '电源管理': 'Power Management',
            '作为用户运行': 'Run As User',
            '删除没有计划的任务': 'Delete Task If Not Rescheduled',
            '如果运行了 X 小时 X 分钟，停止任务': 'Stop Task If Runs X Hours And X Mins',
            '计划': 'Schedule',
            '计划类型': 'Schedule Type',
            '开始时间': 'Start Time',
            '开始日期': 'Start Date',
            '结束日期': 'End Date',
            '天': 'Days',
            '月': 'Months',
            '重复: 每': 'Repeat: Every',
            '重复: 截止: 时间': 'Repeat: Until: Time',
            '重复: 截止: 持续时间': 'Repeat: Until: Duration',
            '重复: 如果还在运行，停止': 'Repeat: Stop If Still Running'
        }

        # 获取表头并映射到英文
        chinese_headers = [h.strip('"') for h in lines[0].split('","')]
        english_headers = [field_mapping.get(h, h) for h in chinese_headers]

        logger.debug(f"解析到的表头: {chinese_headers}")
        logger.debug(f"映射后的表头: {english_headers}")

        # 解析每一行数据
        for line_num, line in enumerate(lines[1:], 2):
            if not line.strip():
                continue

            try:
                # 处理CSV行，考虑到可能包含逗号的字段
                fields = []
                current_field = ""
                in_quotes = False

                for char in line:
                    if char == '"':
                        in_quotes = not in_quotes
                    elif char == ',' and not in_quotes:
                        fields.append(current_field.strip('"'))
                        current_field = ""
                    else:
                        current_field += char

                # 添加最后一个字段
                fields.append(current_field.strip('"'))

                # 创建任务字典
                if len(fields) >= len(english_headers):
                    task = {}
                    for i, header in enumerate(english_headers):
                        task[header] = fields[i] if i < len(fields) else ""
                    tasks.append(task)
                else:
                    logger.warning(f"第{line_num}行字段数量不匹配: 期望{len(english_headers)}，实际{len(fields)}")

            except Exception as e:
                logger.warning(f"解析第{line_num}行时出错: {str(e)}")
                continue

        return tasks

    def _parse_xml_output(self, xml_output: str) -> Dict:
        """
        解析schtasks XML输出
        
        Args:
            xml_output (str): XML格式的输出
            
        Returns:
            Dict: 解析后的任务详细信息
        """
        try:
            root = ET.fromstring(xml_output)
            
            # 定义XML命名空间
            ns = {'task': 'http://schemas.microsoft.com/windows/2004/02/mit/task'}
            
            task_detail = {
                'name': '',
                'description': '',
                'author': '',
                'version': '',
                'date': '',
                'triggers': [],
                'actions': [],
                'settings': {},
                'principals': {}
            }
            
            # 解析基本信息
            registration_info = root.find('task:RegistrationInfo', ns)
            if registration_info is not None:
                task_detail['description'] = self._get_element_text(registration_info.find('task:Description', ns))
                task_detail['author'] = self._get_element_text(registration_info.find('task:Author', ns))
                task_detail['version'] = self._get_element_text(registration_info.find('task:Version', ns))
                task_detail['date'] = self._get_element_text(registration_info.find('task:Date', ns))
            
            # 解析触发器
            triggers = root.find('task:Triggers', ns)
            if triggers is not None:
                for trigger in triggers:
                    trigger_info = self._parse_trigger(trigger, ns)
                    if trigger_info:
                        task_detail['triggers'].append(trigger_info)
            
            # 解析操作
            actions = root.find('task:Actions', ns)
            if actions is not None:
                for action in actions:
                    action_info = self._parse_action(action, ns)
                    if action_info:
                        task_detail['actions'].append(action_info)
            
            # 解析设置
            settings = root.find('task:Settings', ns)
            if settings is not None:
                task_detail['settings'] = self._parse_settings(settings, ns)
            
            # 解析主体信息
            principals = root.find('task:Principals', ns)
            if principals is not None:
                task_detail['principals'] = self._parse_principals(principals, ns)
            
            return task_detail
            
        except ET.ParseError as e:
            logger.error(f"XML解析错误: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"解析XML时发生异常: {str(e)}")
            return {}
    
    def _get_element_text(self, element) -> str:
        """安全获取XML元素文本"""
        return element.text if element is not None and element.text else ""
    
    def _parse_trigger(self, trigger, ns) -> Dict:
        """解析触发器信息"""
        trigger_info = {
            'type': trigger.tag.split('}')[-1] if '}' in trigger.tag else trigger.tag,
            'enabled': trigger.get('enabled', 'true'),
            'start_boundary': '',
            'end_boundary': '',
            'repetition': {}
        }
        
        # 获取开始和结束时间
        start_boundary = trigger.find('task:StartBoundary', ns)
        if start_boundary is not None:
            trigger_info['start_boundary'] = start_boundary.text
        
        end_boundary = trigger.find('task:EndBoundary', ns)
        if end_boundary is not None:
            trigger_info['end_boundary'] = end_boundary.text
        
        # 解析重复设置
        repetition = trigger.find('task:Repetition', ns)
        if repetition is not None:
            interval = repetition.find('task:Interval', ns)
            duration = repetition.find('task:Duration', ns)
            if interval is not None:
                trigger_info['repetition']['interval'] = interval.text
            if duration is not None:
                trigger_info['repetition']['duration'] = duration.text
        
        return trigger_info
    
    def _parse_action(self, action, ns) -> Dict:
        """解析操作信息"""
        action_info = {
            'type': action.tag.split('}')[-1] if '}' in action.tag else action.tag,
            'command': '',
            'arguments': '',
            'working_directory': ''
        }
        
        # 解析执行操作
        if 'Exec' in action_info['type']:
            command = action.find('task:Command', ns)
            if command is not None:
                action_info['command'] = command.text
            
            arguments = action.find('task:Arguments', ns)
            if arguments is not None:
                action_info['arguments'] = arguments.text
            
            working_dir = action.find('task:WorkingDirectory', ns)
            if working_dir is not None:
                action_info['working_directory'] = working_dir.text
        
        return action_info
    
    def _parse_settings(self, settings, ns) -> Dict:
        """解析设置信息"""
        settings_info = {}
        
        for setting in settings:
            tag_name = setting.tag.split('}')[-1] if '}' in setting.tag else setting.tag
            settings_info[tag_name] = setting.text if setting.text else setting.get('enabled', '')
        
        return settings_info
    
    def _parse_principals(self, principals, ns) -> Dict:
        """解析主体信息"""
        principals_info = {}
        
        principal = principals.find('task:Principal', ns)
        if principal is not None:
            user_id = principal.find('task:UserId', ns)
            if user_id is not None:
                principals_info['user_id'] = user_id.text
            
            logon_type = principal.find('task:LogonType', ns)
            if logon_type is not None:
                principals_info['logon_type'] = logon_type.text
            
            run_level = principal.find('task:RunLevel', ns)
            if run_level is not None:
                principals_info['run_level'] = run_level.text
        
        return principals_info
