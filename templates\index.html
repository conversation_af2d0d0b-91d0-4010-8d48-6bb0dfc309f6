<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windows定时任务查看器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-clock"></i> Windows定时任务查看器</h1>
            <p class="subtitle">查看和管理Windows系统中的所有定时任务</p>
        </header>

        <div class="controls">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="搜索任务名称、状态或命令...">
            </div>
            <div class="filter-controls">
                <select id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="就绪">就绪</option>
                    <option value="运行中">运行中</option>
                    <option value="已禁用">已禁用</option>
                    <option value="排队中">排队中</option>
                </select>
                <select id="folderFilter">
                    <option value="">所有文件夹</option>
                </select>
                <button id="refreshBtn" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalTasks">-</div>
                <div class="stat-label">总任务数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="enabledTasks">-</div>
                <div class="stat-label">已启用</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="runningTasks">-</div>
                <div class="stat-label">运行中</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="disabledTasks">-</div>
                <div class="stat-label">已禁用</div>
            </div>
        </div>

        <div class="loading" id="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <span>正在加载任务列表...</span>
        </div>

        <div class="error-message" id="errorMessage" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorText"></span>
        </div>

        <div class="task-table-container">
            <table class="task-table" id="taskTable">
                <thead>
                    <tr>
                        <th class="sortable" data-sort="name">
                            任务名称 <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="status_cn">
                            状态 <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="folder">
                            文件夹 <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="next_run_time">
                            下次运行时间 <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="last_run_time">
                            上次运行时间 <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="last_result_text">
                            上次结果 <i class="fas fa-sort"></i>
                        </th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="taskTableBody">
                    <!-- 任务数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <div class="pagination" id="pagination">
            <button id="prevPage" class="btn btn-secondary">
                <i class="fas fa-chevron-left"></i> 上一页
            </button>
            <span id="pageInfo">第 1 页，共 1 页</span>
            <button id="nextPage" class="btn btn-secondary">
                下一页 <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <!-- 任务详情模态框 -->
    <div class="modal" id="taskModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">任务详情</h2>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 任务详情内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
