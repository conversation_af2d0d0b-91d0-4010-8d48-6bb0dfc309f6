#!/usr/bin/env python3
"""
Windows定时任务查看器测试脚本
"""
import unittest
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from task_manager import WindowsTaskManager
from data_processor import TaskDataProcessor

class TestWindowsTaskViewer(unittest.TestCase):
    """测试Windows定时任务查看器"""
    
    def setUp(self):
        """测试前设置"""
        self.app = create_app('development')
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        self.task_manager = WindowsTaskManager()
        self.data_processor = TaskDataProcessor()
    
    def test_app_creation(self):
        """测试应用创建"""
        self.assertIsNotNone(self.app)
        self.assertTrue(self.app.config['TESTING'])
    
    def test_index_page(self):
        """测试主页面"""
        with self.app.app_context():
            response = self.client.get('/')
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'Windows', response.data)
    
    def test_health_check(self):
        """测试健康检查接口"""
        with self.app.app_context():
            response = self.client.get('/api/health')
            self.assertEqual(response.status_code, 200)

            data = json.loads(response.data)
            self.assertTrue(data['success'])
            self.assertEqual(data['status'], 'healthy')
            self.assertIn('timestamp', data)
    
    def test_tasks_api(self):
        """测试任务列表API"""
        with self.app.app_context():
            response = self.client.get('/api/tasks')
            self.assertEqual(response.status_code, 200)

            data = json.loads(response.data)
            self.assertIn('success', data)
            self.assertIn('data', data)
            self.assertIn('count', data)

            if data['success']:
                self.assertIsInstance(data['data'], list)
                self.assertIsInstance(data['count'], int)
    
    def test_task_manager(self):
        """测试任务管理器"""
        # 测试获取任务列表
        tasks = self.task_manager.get_all_tasks()
        self.assertIsInstance(tasks, list)
        
        # 如果有任务，测试第一个任务的结构
        if tasks:
            task = tasks[0]
            self.assertIn('TaskName', task)
            self.assertIn('Status', task)
    
    def test_data_processor(self):
        """测试数据处理器"""
        # 创建测试数据
        test_task = {
            'TaskName': '\\TestTask',
            'Status': 'Ready',
            'Next Run Time': '2024-01-01 12:00:00',
            'Last Run Time': '2024-01-01 11:00:00',
            'Last Result': '0',
            'Author': 'TestUser',
            'Task To Run': 'test.exe',
            'Comment': 'Test task'
        }
        
        # 测试格式化单个任务
        formatted_tasks = self.data_processor.format_tasks([test_task])
        self.assertEqual(len(formatted_tasks), 1)
        
        formatted_task = formatted_tasks[0]
        self.assertEqual(formatted_task['name'], 'TestTask')
        self.assertEqual(formatted_task['status_cn'], '就绪')
        self.assertEqual(formatted_task['folder'], '根目录')
    
    def test_status_mapping(self):
        """测试状态映射"""
        processor = TaskDataProcessor()
        
        test_cases = [
            ('Ready', '就绪'),
            ('Running', '运行中'),
            ('Disabled', '已禁用'),
            ('Unknown', '未知')
        ]
        
        for status, expected in test_cases:
            self.assertEqual(processor.status_mapping.get(status), expected)
    
    def test_datetime_formatting(self):
        """测试日期时间格式化"""
        processor = TaskDataProcessor()
        
        # 测试各种日期格式
        test_cases = [
            ('2024-01-01T12:00:00', '2024-01-01 12:00:00'),
            ('N/A', 'N/A'),
            ('Never', 'Never'),
            ('', '')
        ]
        
        for input_dt, expected in test_cases:
            result = processor._format_datetime(input_dt)
            if expected:
                self.assertIn(expected, result)
    
    def test_result_code_formatting(self):
        """测试结果代码格式化"""
        processor = TaskDataProcessor()
        
        test_cases = [
            ('0', '成功'),
            ('0x0', '成功'),
            ('0x1', '错误的函数'),
            ('unknown_code', '错误代码: unknown_code')
        ]
        
        for code, expected in test_cases:
            result = processor._format_result_code(code)
            self.assertIn(expected, result)
    
    def test_folder_extraction(self):
        """测试文件夹提取"""
        processor = TaskDataProcessor()
        
        test_cases = [
            ('\\Microsoft\\Windows\\Task', 'Microsoft\\Windows'),
            ('\\TestTask', '根目录'),
            ('', '根目录'),
            ('\\SingleFolder\\Task', 'SingleFolder')
        ]
        
        for path, expected in test_cases:
            result = processor._extract_folder_from_path(path)
            self.assertEqual(result, expected)

def run_basic_tests():
    """运行基本功能测试"""
    print("=" * 60)
    print("Windows定时任务查看器 - 基本功能测试")
    print("=" * 60)
    
    # 测试任务管理器
    print("\n1. 测试任务管理器...")
    try:
        task_manager = WindowsTaskManager()
        tasks = task_manager.get_all_tasks()
        print(f"   ✓ 成功获取 {len(tasks)} 个任务")
        
        if tasks:
            print(f"   ✓ 第一个任务: {tasks[0].get('TaskName', 'Unknown')}")
        
    except Exception as e:
        print(f"   ✗ 任务管理器测试失败: {e}")
    
    # 测试数据处理器
    print("\n2. 测试数据处理器...")
    try:
        processor = TaskDataProcessor()
        test_task = {
            'TaskName': '\\TestTask',
            'Status': 'Ready',
            'Next Run Time': '2024-01-01 12:00:00',
            'Last Run Time': '2024-01-01 11:00:00',
            'Last Result': '0'
        }
        
        formatted = processor.format_tasks([test_task])
        print(f"   ✓ 数据格式化成功")
        print(f"   ✓ 任务名称: {formatted[0]['name']}")
        print(f"   ✓ 状态: {formatted[0]['status_cn']}")
        
    except Exception as e:
        print(f"   ✗ 数据处理器测试失败: {e}")
    
    # 测试Web应用
    print("\n3. 测试Web应用...")
    try:
        app = create_app('development')
        app.config['TESTING'] = True
        client = app.test_client()
        
        # 测试主页
        response = client.get('/')
        if response.status_code == 200:
            print("   ✓ 主页访问正常")
        else:
            print(f"   ✗ 主页访问失败: {response.status_code}")
        
        # 测试健康检查
        response = client.get('/api/health')
        if response.status_code == 200:
            print("   ✓ 健康检查接口正常")
        else:
            print(f"   ✗ 健康检查接口失败: {response.status_code}")
        
        # 测试任务API
        response = client.get('/api/tasks')
        if response.status_code == 200:
            data = json.loads(response.data)
            if data.get('success'):
                print(f"   ✓ 任务API正常，返回 {data.get('count', 0)} 个任务")
            else:
                print(f"   ✗ 任务API返回错误: {data.get('error', 'Unknown')}")
        else:
            print(f"   ✗ 任务API访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ✗ Web应用测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--basic':
        run_basic_tests()
    else:
        unittest.main()
